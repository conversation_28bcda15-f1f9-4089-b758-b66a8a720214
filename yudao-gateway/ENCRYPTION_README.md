# 网关层统一加密功能

## 概述

本功能将原来在servlet层的EncryptFilter加密逻辑迁移到网关层，实现统一的加密解密处理。支持多种加密算法，并可以根据不同的请求路径配置不同的加密方式。

## 功能特性

1. **多种加密算法支持**：AES、DES、RSA、Base64等
2. **路径级别配置**：不同的请求路径可以使用不同的加密方式
3. **动态Bean生成**：根据配置文件自动生成对应的加密服务Bean
4. **向后兼容**：保留原有的`/encrypt`接口，使用Base64编码

## 配置说明

### 1. 启用加密功能

在`application.yml`中添加：

```yaml
gateway:
  encryption:
    enable: true  # 启用加密功能
    force: false  # 是否强制加密（true时，未配置加密的路径将被拒绝）
```

### 2. 配置加密服务

```yaml
gateway:
  encryption:
    enable: true
    services:
      # AES加密服务
      aes:
        - name: "/api/user"  # 请求路径
          key: "userServiceKey1234567890123456"  # 32位密钥
          iv: "userServiceIv12"  # 16位IV
          transformation: "AES/CBC/PKCS5Padding"
          key-size: 256
      
      # Base64编码服务
      base64:
        - name: "/api/public"
          charset: "UTF-8"
      
      # DES加密服务
      des:
        - name: "/api/legacy"
          key: "legacyKey12345"  # 8位密钥
          iv: "legacyIv"  # 8位IV
      
      # RSA加密服务
      rsa:
        - name: "/api/secure"
          key: "rsaPublicKey"  # RSA公钥
          key-size: 2048
```

## 使用方式

### 1. 原有接口（向后兼容）

```
POST /encrypt
Content-Type: application/json

{加密后的请求体}
```

### 2. 新的路径级别加密接口

根据配置，系统会自动生成对应的加密路由：

- `/encrypt/api-user` - 使用AES加密处理/api/user路径的请求
- `/encrypt/api-public` - 使用Base64编码处理/api/public路径的请求
- `/encrypt/api-legacy` - 使用DES加密处理/api/legacy路径的请求
- `/encrypt/api-secure` - 使用RSA加密处理/api/secure路径的请求

## 实现原理

1. **配置解析**：`GatewayEncryptionProperties`读取配置文件
2. **服务注册**：`GatewayEncryptionServiceRegistrar`根据配置动态注册加密服务Bean
3. **路由生成**：`GatewayEncryptionAutoConfiguration`为每个加密服务生成对应的路由
4. **请求处理**：`EncryptBridgeRouter`处理加密请求的转发

## 迁移指南

### 从servlet EncryptFilter迁移

1. **禁用原有的servlet加密**：
   ```yaml
   encryption:
     enable: false  # 禁用原有的servlet加密
   ```

2. **启用网关加密**：
   ```yaml
   gateway:
     encryption:
       enable: true
   ```

3. **配置路径映射**：将原来的全局加密配置改为路径级别的配置

### 客户端调用变更

原来的调用方式：
```
POST /api/user
X-Encrypt: true
{加密的请求体}
```

新的调用方式：
```
POST /encrypt/api-user
{加密的请求体}
```

## 注意事项

1. **密钥安全**：生产环境中请使用安全的密钥管理方式
2. **性能考虑**：加密解密会增加一定的性能开销
3. **兼容性**：确保客户端能够正确处理新的加密接口
4. **监控**：建议添加加密相关的监控和日志

## 扩展功能

1. **自定义加密算法**：可以通过实现`EncryptionService`接口添加新的加密算法
2. **动态配置**：可以通过配置中心实现加密配置的动态更新
3. **加密策略**：可以根据用户、时间等条件动态选择加密策略
