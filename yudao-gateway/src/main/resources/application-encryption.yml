# 网关加密配置示例
gateway:
  encryption:
    enable: true
    force: false
    default-key: "defaultEncryptionKey123456"
    default-iv: "defaultEncryptionIv"
    charset: "UTF-8"
    services:
      # AES加密服务配置
      aes:
        - name: "/api/user"  # 对应请求路径
          key: "userServiceKey1234567890123456"  # 32位密钥
          iv: "userServiceIv12"  # 16位IV
          transformation: "AES/CBC/PKCS5Padding"
          key-size: 256
          charset: "UTF-8"
        - name: "/api/order"
          key: "orderServiceKey123456789012345"
          iv: "orderServiceIv1"
          transformation: "AES/CBC/PKCS5Padding"
          key-size: 256
      
      # Base64编码服务配置
      base64:
        - name: "/api/public"  # 公开接口使用Base64编码
          charset: "UTF-8"
        - name: "/api/test"
          charset: "UTF-8"
      
      # DES加密服务配置
      des:
        - name: "/api/legacy"  # 遗留系统使用DES
          key: "legacyKey12345"  # 8位密钥
          iv: "legacyIv"  # 8位IV
          transformation: "DES/CBC/PKCS5Padding"
      
      # RSA加密服务配置
      rsa:
        - name: "/api/secure"  # 高安全要求接口使用RSA
          key: "rsaPublicKey"  # 实际应该是完整的RSA公钥
          transformation: "RSA/ECB/PKCS1Padding"
          key-size: 2048
