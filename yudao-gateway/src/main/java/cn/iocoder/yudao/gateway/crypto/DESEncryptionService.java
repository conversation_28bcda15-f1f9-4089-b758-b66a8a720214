package cn.iocoder.yudao.gateway.crypto;

import lombok.Setter;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import javax.crypto.spec.IvParameterSpec;
import java.security.SecureRandom;

/**
 * DES对称加密服务实现
 */
public class DESEncryptionService extends AbstractEncryptionService {

    /**
     * DES算法名称
     */
    private static final String ALGORITHM = "DES";

    /**
     * DES加密模式和填充方式
     */
    @Setter
    private String transformation;

    /**
     * 默认构造函数
     */
    public DESEncryptionService() {
        this.transformation = "DES/CBC/PKCS5Padding";
    }

    /**
     * 手动构造函数
     *
     * @param transformation 转换模式
     */
    public DESEncryptionService(String transformation) {
        this.transformation = transformation;
        validateParameters();
    }

    /**
     * 验证参数
     */
    private void validateParameters() {
        if (transformation == null || transformation.isEmpty()) {
            throw new IllegalArgumentException("DES transformation cannot be null or empty");
        }
    }

    @Override
    public String getType() {
        return "DES";
    }

    @Override
    public String encrypt(String data, String key, String iv) throws EncryptionException {
        try {
            Cipher cipher = Cipher.getInstance(transformation);
            SecretKey secretKey = createKey(key);
            IvParameterSpec ivSpec = new IvParameterSpec(base64ToBytes(iv));

            cipher.init(Cipher.ENCRYPT_MODE, secretKey, ivSpec);
            byte[] encrypted = cipher.doFinal(data.getBytes(getCharset()));

            return bytesToBase64(encrypted);
        } catch (Exception e) {
            throw new EncryptionException("DES encryption failed", e);
        }
    }

    @Override
    public String decrypt(String encryptedData, String key, String iv) throws EncryptionException {
        try {
            Cipher cipher = Cipher.getInstance(transformation);
            SecretKey secretKey = createKey(key);
            IvParameterSpec ivSpec = new IvParameterSpec(base64ToBytes(iv));

            cipher.init(Cipher.DECRYPT_MODE, secretKey, ivSpec);
            byte[] decrypted = cipher.doFinal(base64ToBytes(encryptedData));

            return new String(decrypted, getCharset());
        } catch (Exception e) {
            throw new EncryptionException("DES decryption failed", e);
        }
    }

    @Override
    public String generateIv() {
        try {
            SecureRandom random = new SecureRandom();
            byte[] iv = new byte[8]; // DES block size is 8 bytes
            random.nextBytes(iv);
            return bytesToBase64(iv);
        } catch (Exception e) {
            throw new EncryptionException("Failed to generate IV", e);
        }
    }

    /**
     * 创建DES密钥
     *
     * @param key 密钥字符串
     * @return 密钥
     */
    private SecretKey createKey(String key) throws Exception {
        byte[] keyBytes = key.getBytes(getCharset());
        // DES密钥长度必须是8字节
        if (keyBytes.length < 8) {
            byte[] newKey = new byte[8];
            System.arraycopy(keyBytes, 0, newKey, 0, keyBytes.length);
            for (int i = keyBytes.length; i < 8; i++) {
                newKey[i] = 0;
            }
            keyBytes = newKey;
        } else if (keyBytes.length > 8) {
            byte[] newKey = new byte[8];
            System.arraycopy(keyBytes, 0, newKey, 0, 8);
            keyBytes = newKey;
        }

        DESKeySpec desKeySpec = new DESKeySpec(keyBytes);
        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance(ALGORITHM);
        return keyFactory.generateSecret(desKeySpec);
    }
}
