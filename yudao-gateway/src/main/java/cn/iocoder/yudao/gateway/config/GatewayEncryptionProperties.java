package cn.iocoder.yudao.gateway.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;

/**
 * 网关加密配置属性
 */
@ConfigurationProperties(prefix = "gateway.encryption")
@Data
public class GatewayEncryptionProperties {
    
    /**
     * 是否启用加密
     */
    private boolean enable = false;
    
    /**
     * 强制加密
     */
    private boolean force = false;
    
    /**
     * 默认密钥
     */
    private String defaultKey;
    
    /**
     * 默认IV
     */
    private String defaultIv;
    
    /**
     * 字符编码
     */
    private String charset = "UTF-8";
    
    /**
     * 加密服务配置
     */
    private ServicesConfig services = new ServicesConfig();
    
    /**
     * 加密服务配置
     */
    @Data
    public static class ServicesConfig {
        /**
         * AES加密服务配置列表
         */
        private List<AesServiceConfig> aes = new ArrayList<>();
        
        /**
         * AES-GCM加密服务配置列表
         */
        private List<AesGcmServiceConfig> aesGcm = new ArrayList<>();
        
        /**
         * AES-PBE加密服务配置列表
         */
        private List<AesPBEServiceConfig> aesPBE = new ArrayList<>();
        
        /**
         * DES加密服务配置列表
         */
        private List<DesServiceConfig> des = new ArrayList<>();
        
        /**
         * RSA加密服务配置列表
         */
        private List<RsaServiceConfig> rsa = new ArrayList<>();
        
        /**
         * Base64编码服务配置列表
         */
        private List<Base64ServiceConfig> base64 = new ArrayList<>();
    }
    
    /**
     * AES加密服务配置
     */
    @Data
    public static class AesServiceConfig extends BaseServiceConfig {
        /**
         * 转换模式
         */
        private String transformation = "AES/CBC/PKCS5Padding";
        
        /**
         * 密钥长度
         */
        private int keySize = 256;
    }
    
    /**
     * DES加密服务配置
     */
    @Data
    public static class DesServiceConfig extends BaseServiceConfig {
        /**
         * 转换模式
         */
        private String transformation = "DES/CBC/PKCS5Padding";
    }
    
    /**
     * RSA加密服务配置
     */
    @Data
    public static class RsaServiceConfig extends BaseServiceConfig {
        /**
         * 转换模式
         */
        private String transformation = "RSA/ECB/PKCS1Padding";
        
        /**
         * 密钥长度
         */
        private int keySize = 2048;
    }
    
    /**
     * AES-GCM加密服务配置
     */
    @Data
    public static class AesGcmServiceConfig extends AesServiceConfig {
    }
    
    /**
     * AES-PBE加密服务配置
     */
    @Data
    public static class AesPBEServiceConfig extends AesServiceConfig {
        /**
         * 迭代数
         */
        private int iterations = 1000;
        /**
         * 加盐
         */
        private String salt;
    }
    
    /**
     * Base64编码服务配置
     */
    @Data
    public static class Base64ServiceConfig extends BaseServiceConfig {
    }
    
    @Data
    public static class BaseServiceConfig {
        /**
         * Bean名称（对应请求路径）
         */
        private String name;
        
        /**
         * 密钥
         */
        private String key;
        
        /**
         * IV
         */
        private String iv;
        
        /**
         * 字符编码
         */
        private String charset;
    }
}
