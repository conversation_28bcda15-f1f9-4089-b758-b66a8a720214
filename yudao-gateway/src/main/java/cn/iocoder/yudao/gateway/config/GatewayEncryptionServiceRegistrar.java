package cn.iocoder.yudao.gateway.config;

import cn.iocoder.yudao.gateway.crypto.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.beans.factory.support.BeanDefinitionRegistryPostProcessor;
import org.springframework.boot.context.properties.bind.Binder;
import org.springframework.core.env.Environment;

/**
 * 网关加密服务注册器
 *
 * 根据配置动态注册多个加密服务Bean
 * 使用BeanDefinitionRegistryPostProcessor在Bean定义阶段注册加密服务
 */
@Slf4j
public class GatewayEncryptionServiceRegistrar implements BeanDefinitionRegistryPostProcessor {

    private Environment environment;

    public GatewayEncryptionServiceRegistrar(Environment environment) {
        this.environment = environment;
    }

    @Override
    public void postProcessBeanDefinitionRegistry(BeanDefinitionRegistry registry) throws BeansException {
        // 从环境中绑定配置属性
        GatewayEncryptionProperties properties = Binder.get(environment)
                .bind("gateway.encryption", GatewayEncryptionProperties.class)
                .orElse(new GatewayEncryptionProperties());

        if (!properties.isEnable()) {
            log.debug("Gateway encryption is disabled");
            return;
        }

        if (properties.getServices() == null) {
            log.debug("No gateway encryption services configured");
            return;
        }

        // 注册 AES 加密服务
        if (properties.getServices().getAes() != null) {
            for (GatewayEncryptionProperties.AesServiceConfig config : properties.getServices().getAes()) {
                registerEncryptionService(registry, config, AESEncryptionService.class);
            }
        }

        // 注册 DES 加密服务
        if (properties.getServices().getDes() != null) {
            for (GatewayEncryptionProperties.DesServiceConfig config : properties.getServices().getDes()) {
                registerEncryptionService(registry, config, DESEncryptionService.class);
            }
        }

        // 注册 RSA 加密服务
        if (properties.getServices().getRsa() != null) {
            for (GatewayEncryptionProperties.RsaServiceConfig config : properties.getServices().getRsa()) {
                registerEncryptionService(registry, config, RSAEncryptionService.class);
            }
        }

        // 注册 AES-GCM 加密服务
        if (properties.getServices().getAesGcm() != null) {
            for (GatewayEncryptionProperties.AesGcmServiceConfig config : properties.getServices().getAesGcm()) {
                registerEncryptionService(registry, config, AESGCMEncryptionService.class);
            }
        }

        // 注册 AES-PBE 加密服务
        if (properties.getServices().getAesPBE() != null) {
            for (GatewayEncryptionProperties.AesPBEServiceConfig config : properties.getServices().getAesPBE()) {
                registerEncryptionService(registry, config, AESPBEEncryptionService.class);
            }
        }

        // 注册 Base64 编码服务
        if (properties.getServices().getBase64() != null) {
            for (GatewayEncryptionProperties.Base64ServiceConfig config : properties.getServices().getBase64()) {
                registerEncryptionService(registry, config, Base64EncodingService.class);
            }
        }

        log.info("Gateway encryption services registration completed");
    }

    @Override
    public void postProcessBeanFactory(ConfigurableListableBeanFactory beanFactory) throws BeansException {
        // 在这个阶段可以进行Bean的后处理，但对于我们的用例，主要工作在postProcessBeanDefinitionRegistry中完成
    }

    /**
     * 通用的加密服务注册方法
     */
    private <T extends GatewayEncryptionProperties.BaseServiceConfig, S extends EncryptionService> void registerEncryptionService(
            BeanDefinitionRegistry registry, T config, Class<S> serviceClass) {
        try {
            String name = config.getName();
            if (name == null || name.isEmpty()) {
                log.debug("Skipping {} service with empty name", serviceClass.getSimpleName());
                return;
            }

            if (registry.containsBeanDefinition(name)) {
                log.debug("Bean definition already exists: {}", name);
                return;
            }

            // 创建Bean定义
            BeanDefinitionBuilder builder = BeanDefinitionBuilder.genericBeanDefinition(serviceClass);

            // 设置构造函数参数
            if (serviceClass == AESEncryptionService.class) {
                GatewayEncryptionProperties.AesServiceConfig aesConfig = (GatewayEncryptionProperties.AesServiceConfig) config;
                builder.addConstructorArgValue(aesConfig.getTransformation());
                builder.addConstructorArgValue(aesConfig.getKeySize());
            } else if (serviceClass == DESEncryptionService.class) {
                GatewayEncryptionProperties.DesServiceConfig desConfig = (GatewayEncryptionProperties.DesServiceConfig) config;
                builder.addConstructorArgValue(desConfig.getTransformation());
            } else if (serviceClass == RSAEncryptionService.class) {
                GatewayEncryptionProperties.RsaServiceConfig rsaConfig = (GatewayEncryptionProperties.RsaServiceConfig) config;
                builder.addConstructorArgValue(rsaConfig.getTransformation());
                builder.addConstructorArgValue(rsaConfig.getKeySize());
            } else if (serviceClass == AESGCMEncryptionService.class) {
                GatewayEncryptionProperties.AesGcmServiceConfig gcmConfig = (GatewayEncryptionProperties.AesGcmServiceConfig) config;
                builder.addConstructorArgValue(gcmConfig.getKeySize());
            } else if (serviceClass == AESPBEEncryptionService.class) {
                GatewayEncryptionProperties.AesPBEServiceConfig pbeConfig = (GatewayEncryptionProperties.AesPBEServiceConfig) config;
                builder.addConstructorArgValue(pbeConfig.getTransformation());
                builder.addConstructorArgValue(pbeConfig.getKeySize());
                builder.addConstructorArgValue(pbeConfig.getIterations());
                builder.addConstructorArgValue(pbeConfig.getSalt());
            }
            // Base64EncodingService 使用默认构造函数，不需要设置构造函数参数

            // 设置属性
            if (config.getKey() != null) {
                builder.addPropertyValue("defaultKey", config.getKey());
            }
            if (config.getIv() != null) {
                builder.addPropertyValue("defaultIv", config.getIv());
            }
            if (config.getCharset() != null) {
                builder.addPropertyValue("charset", config.getCharset());
            }
            builder.addPropertyValue("name", name);

            // 注册Bean
            registry.registerBeanDefinition(name, builder.getBeanDefinition());
            log.info("Registered encryption service: {} -> {}", name, serviceClass.getSimpleName());

        } catch (Exception e) {
            log.error("Failed to register encryption service: {}", config.getName(), e);
        }
    }
}
