package cn.iocoder.yudao.gateway.crypto;

/**
 * 加密解密异常
 * 
 * 用于封装加密解密过程中的各种异常
 */
public class EncryptionException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    /**
     * 构造一个新的加密异常
     * 
     * @param message 异常信息
     */
    public EncryptionException(String message) {
        super(message);
    }

    /**
     * 构造一个新的加密异常
     * 
     * @param message 异常信息
     * @param cause 原始异常
     */
    public EncryptionException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * 构造一个新的加密异常
     * 
     * @param cause 原始异常
     */
    public EncryptionException(Throwable cause) {
        super(cause);
    }
}
