package cn.iocoder.yudao.gateway.route;

import cn.hutool.core.codec.Base64;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.gateway.crypto.EncryptionService;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.server.*;
import reactor.core.publisher.Mono;

import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * 加密桥接路由器
 * <p>
 * 该类实现了一个加密通信桥接功能，用于客户端和内部微服务/外部API 之间的加密通信。
 * 整个处理流程如下：
 * 1. 客户端发送加密的JSON数据到对应的加密路径
 * 2. 服务端根据路径选择对应的加密服务进行解密
 * 3. 解析出请求中的{url, method, body, parameter}参数
 * 4. 转发客户端请求头到内部请求
 * 5. 使用WebClient调用真实的内部微服务（明文通信）
 * 6. 接收内部服务的响应（明文）
 * 7. 使用对应的加密服务加密响应内容
 * 8. 将加密后的响应返回给客户端
 * <p>
 * 支持多种加密算法：AES、DES、RSA、Base64等，根据配置动态生成路由。
 *
 * <AUTHOR>
 */
@AllArgsConstructor
public class EncryptBridgeRouter implements HandlerFunction<ServerResponse> {

    private static final Logger log = LoggerFactory.getLogger(EncryptBridgeRouter.class);
    private final WebClient webClient = WebClient.builder().build();

    private final EncryptionService encryptionService;

    @Override
    public Mono<ServerResponse> handle(ServerRequest request) {
        // 读取整个请求体
        return request.bodyToMono(String.class)
                .flatMap(encryptedBody -> {
                    try {
                        log.debug("Received encrypted request: {}", encryptedBody);

                        // 使用解密请求内容
                        String decrypted = encryptionService.decrypt(encryptedBody);
                        log.debug("Decrypted request content: {}", decrypted);

                        // 解析JSON对象
                        JSONObject json = JSONUtil.parseObj(decrypted);
                        String url = json.getStr("url");
                        String method = json.getStr("method", "POST");
                        String body = json.getStr("body");
                        JSONObject params = json.getJSONObject("parameter");

                        log.debug("Forwarding to internal service: {} {}, params: {}, body: {}",
                                method, url, params, body);

                        // 获取原始请求中的所有header
                        HttpHeaders headers = request.headers().asHttpHeaders();
                        log.debug("Forwarding headers: {}", headers);

                        // 调用内部服务，并转发所有header
                        WebClient.RequestBodySpec reqSpec = webClient.method(HttpMethod.valueOf(method))
                                .uri(builder -> {
                                    URI targetUri = URI.create(url);
                                    if (!targetUri.isAbsolute()) {
                                        // 相对路径 -> 内部调用
                                        builder.path(url);
                                        if (params != null) {
                                            params.forEach((k, v) -> builder.queryParam(k, v));
                                        }
                                        return builder.build();
                                    } else {
                                        // 绝对路径 -> 外部调用
                                        return targetUri;
                                    }
                                })
                                .contentType(MediaType.APPLICATION_JSON);

                        // 转发header到内部服务
                        for (Map.Entry<String, String> entry : headers.toSingleValueMap().entrySet()) {
                            // 避免覆盖已设置的content-type等header
                            if (!entry.getKey().equalsIgnoreCase(HttpHeaders.CONTENT_TYPE)) {
                                reqSpec = reqSpec.header(entry.getKey(), entry.getValue());
                            }
                        }

                        Mono<String> responseMono = reqSpec
                                .body(BodyInserters.fromValue(body))
                                .retrieve()
                                .bodyToMono(String.class);

                        return responseMono.flatMap(resp -> {
                            log.debug("Received response from internal service: {}", resp);

                            // 使用加密响应内容
                            String encryptedResp = encryptionService.encrypt(resp);
                            log.debug("Encrypted response: {}", encryptedResp);

                            return ServerResponse.ok()
                                    .contentType(MediaType.TEXT_PLAIN)
                                    .bodyValue(encryptedResp);
                        });
                    } catch (Exception e) {
                        log.error("Error processing encrypted request", e);
                        return ServerResponse.status(500)
                                .contentType(MediaType.TEXT_PLAIN)
                                .bodyValue("Error processing encrypted request: " + e.getMessage());
                    }
                });
    }
}
