package cn.iocoder.yudao.gateway.crypto;

import lombok.Data;

import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.PBEKeySpec;
import javax.crypto.spec.SecretKeySpec;

/**
 * AES对称加密服务实现
 *
 * <pre>
 *      前端无法直接用 Java 的 SecureRandom("SHA1PRNG") 密钥派生逻辑
 *      前端使用的是 CryptoJS 的 PBKDF2 + MD5 派生 AES key（这是最容易前端实现的）
 * </pre>
 *
 * <code>
 *       <script src="https://cdn.jsdelivr.net/npm/crypto-js@4.1.1/crypto-js.min.js"></script>
 *
 *      function decrypt(base64Ciphertext, keyStr, ivBase64) {
 *   const salt = CryptoJS.enc.Utf8.parse("mysalt1234");
 *   const derivedKey = CryptoJS.PBKDF2(keyStr, salt, {
 *     keySize: 128 / 32,
 *     iterations: 1000,
 *     hasher: CryptoJS.algo.SHA256
 *   });
 *
 *   const iv = CryptoJS.enc.Base64.parse(ivBase64);
 *   const encryptedHex = CryptoJS.enc.Base64.parse(base64Ciphertext);
 *
 *   const decrypted = CryptoJS.AES.decrypt(
 *     { ciphertext: encryptedHex },
 *     derivedKey,
 *     {
 *       iv: iv,
 *       mode: CryptoJS.mode.CBC,
 *       padding: CryptoJS.pad.Pkcs7
 *     }
 *   );
 *
 *   return decrypted.toString(CryptoJS.enc.Utf8);
 * }
 * </code>
 *
 */
@Data
public class AESPBEEncryptionService extends AESEncryptionService {

    /**
     * AES算法名称
     */
    private static final String ALGORITHM = "AES";

    private int iterations;

    private String salt;

    public AESPBEEncryptionService(String transformation, int keySize) {
        super(transformation, keySize);
    }

    /**
     * 创建AES密钥规范
     *
     * @param key 密钥字符串
     * @return 密钥规范
     */
    protected SecretKeySpec createKeySpec(String key) throws Exception {
        // 与前端一致：固定 saltByte、1000 次迭代、SHA-256
        byte[] saltByte = salt.getBytes(getCharset()); // 与前端保持一致

        PBEKeySpec spec = new PBEKeySpec(key.toCharArray(), saltByte, iterations, keySize);
        SecretKeyFactory factory = SecretKeyFactory.getInstance("PBKDF2WithHmacSHA256");
        byte[] keyBytes = factory.generateSecret(spec).getEncoded();

        return new SecretKeySpec(keyBytes, ALGORITHM);
    }

    public static void main(String[] args) {
        AESPBEEncryptionService aesEncryptionService = new AESPBEEncryptionService("AES/CBC/PKCS5Padding", 128);
        aesEncryptionService.iterations = 1000;
        aesEncryptionService.salt = "mysalt1234";


        String data = "asdbimo//ljx89FPysVysgYPRdf+r66ZG74ThK7JRW4=";
        //
        String key = "rZr8it3YHJj6eiQLbG";
        String iv = "ZtWI+MF8xFdQo8C/iLkRTg==";
        System.out.println(aesEncryptionService.decrypt(data, key, iv));

        String plaintext = "前端你好 👋";

        String encryptedBase64 = aesEncryptionService.encrypt(plaintext, key, iv);
        System.out.println("加密后 (Base64): " + encryptedBase64);
    }
}
