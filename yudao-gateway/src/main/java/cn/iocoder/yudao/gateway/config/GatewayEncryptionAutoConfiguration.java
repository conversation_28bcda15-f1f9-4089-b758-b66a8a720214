package cn.iocoder.yudao.gateway.config;

import cn.iocoder.yudao.gateway.crypto.EncryptionService;
import cn.iocoder.yudao.gateway.route.EncryptBridgeRouter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.web.reactive.function.server.RequestPredicates;
import org.springframework.web.reactive.function.server.RouterFunction;
import org.springframework.web.reactive.function.server.RouterFunctions;
import org.springframework.web.reactive.function.server.ServerResponse;

import java.util.Map;

/**
 * 网关加密自动配置类
 */
@AutoConfiguration
@Slf4j
@EnableConfigurationProperties(GatewayEncryptionProperties.class)
@ConditionalOnProperty(prefix = "gateway.encryption", name = "enable", havingValue = "true")
public class GatewayEncryptionAutoConfiguration {

    /**
     * 创建动态加密服务注册器
     */
    @Bean
    public GatewayEncryptionServiceRegistrar gatewayEncryptionServiceRegistrar(
            GatewayEncryptionProperties properties, ApplicationContext applicationContext) {
        return new GatewayEncryptionServiceRegistrar(properties, applicationContext);
    }

    /**
     * 创建动态加密路由
     */
    @Bean
    public RouterFunction<ServerResponse> dynamicEncryptRoutes(ApplicationContext applicationContext) {
        RouterFunction<ServerResponse> routes = RouterFunctions.route()
                .build();

        // 获取所有加密服务，为每个服务创建对应的路由
        Map<String, EncryptionService> encryptionServices = applicationContext.getBeansOfType(EncryptionService.class);
        for (Map.Entry<String, EncryptionService> entry : encryptionServices.entrySet()) {
            String serviceName = entry.getKey();
            EncryptionService service = entry.getValue();
            // 为每个加密服务创建对应的路由路径
            routes = routes.and(RouterFunctions.route(
                RequestPredicates.POST("/encrypt/" + serviceName), new EncryptBridgeRouter(service)
            ));
            log.info("Created encryption route: /encrypt/{} -> {}", serviceName, service.getType());
        }
        
        return routes;
    }
}
