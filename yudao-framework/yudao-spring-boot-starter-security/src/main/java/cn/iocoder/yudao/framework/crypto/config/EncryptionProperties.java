package cn.iocoder.yudao.framework.crypto.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.ArrayList;
import java.util.List;

/**
 * 加密配置属性
 */
@ConfigurationProperties(prefix = "encryption")
@Data
public class EncryptionProperties {
    /**
     * 强制加密
     */
    private boolean force;
    /**
     * 默认密钥
     */
    private String defaultKey ;

    /**
     * 默认IV
     */
    private String defaultIv ;

    /**
     * 字符编码
     */
    private String charset ;

    /**
     * 加密服务配置
     */
    private ServicesConfig services = new ServicesConfig();

    /**
     * 加密服务配置
     */
    @Data
    public static class ServicesConfig {
        /**
         * AES加密服务配置列表
         */
        private List<AesServiceConfig> aes = new ArrayList<>();

        /**
         * AES-GCM加密服务配置列表
         */
        private List<AesGcmServiceConfig> aesGcm = new ArrayList<>();

        /**
         * AES-GCM加密服务配置列表
         */
        private List<AesPBEServiceConfig> aesPBE = new ArrayList<>();

        /**
         * DES加密服务配置列表
         */
        private List<DesServiceConfig> des = new ArrayList<>();

        /**
         * RSA加密服务配置列表
         */
        private List<RsaServiceConfig> rsa = new ArrayList<>();

        /**
         * Base64编码服务配置列表
         */
        private List<Base64ServiceConfig> base64 = new ArrayList<>();
    }

    /**
     * AES加密服务配置
     */
    @Data
    public static class AesServiceConfig extends BaseServiceConfig{
        /**
         * 转换模式
         */
        private String transformation;

        /**
         * 密钥长度
         */
        private int keySize = 256;
    }

    /**
     * DES加密服务配置
     */
    @Data
    public static class DesServiceConfig extends BaseServiceConfig{
        /**
         * 转换模式
         */
        private String transformation;
    }

    /**
     * RSA加密服务配置
     */
    @Data
    public static class RsaServiceConfig extends BaseServiceConfig{
        /**
         * 转换模式
         */
        private String transformation;

        /**
         * 密钥长度
         */
        private int keySize = 2048;
    }

    /**
     * AES-GCM加密服务配置
     */
    @Data
    public static class AesGcmServiceConfig extends AesServiceConfig{
    }

    /**
     * AES-PBE加密服务配置
     */
    @Data
    public static class AesPBEServiceConfig extends AesServiceConfig{
        /**
         * 迭代数
         */
        private int iterations;
        /**
         * 加盐
         */
        private String salt;
    }

    /**
     * Base64编码服务配置
     */
    @Data
    public static class Base64ServiceConfig extends BaseServiceConfig{
    }

    @Data
    public static class BaseServiceConfig {
        /**
         * Bean名称
         */
        private String name;
        /**
         * 字符编码
         */
        private String charset;
    }
}
