package cn.iocoder.yudao.framework.crypto.config;

import cn.iocoder.yudao.framework.crypto.filter.EncryptFilter;
import cn.iocoder.yudao.module.system.api.crypto.CryptoApi;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.core.Ordered;

import javax.annotation.Resource;

/**
 * 加密服务配置类
 */
@AutoConfiguration
@ConditionalOnProperty(prefix = "encryption", value = "enable")
@EnableConfigurationProperties(EncryptionProperties.class)
public class YudaoServletAutoConfiguration {

    @Resource
    EncryptionProperties encryptionProperties;

    /**
     * 创建动态 Bean 注册器
     */
    @Bean
    public EncryptionServiceRegistrar encryptionServiceBeanRegistrar(EncryptFilter encryptFilter, ApplicationContext applicationContext) {
        return new EncryptionServiceRegistrar(encryptionProperties,applicationContext,encryptFilter);
    }

    /**
     * 创建 EncryptFilter Bean
     */
    @Bean
    public EncryptFilter encryptFilter(CryptoApi cryptoApi) {
        return new EncryptFilter(cryptoApi,encryptionProperties.isForce());
    }

    /**
     * 注册 EncryptFilter 过滤器
     */
    @Bean
    public FilterRegistrationBean<EncryptFilter> encryptFilterRegistration(EncryptFilter encryptFilter) {
        FilterRegistrationBean<EncryptFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(encryptFilter);
        registrationBean.setOrder(Ordered.HIGHEST_PRECEDENCE);
        return registrationBean;
    }

}
