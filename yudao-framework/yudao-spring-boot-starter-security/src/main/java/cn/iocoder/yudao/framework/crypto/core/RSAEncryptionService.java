package cn.iocoder.yudao.framework.crypto.core;

import lombok.Setter;

import javax.crypto.Cipher;
import java.security.*;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;

/**
 * RSA加密服务实现
 *
 * 注意：RSA算法不使用IV，但为了保持接口一致性，仍然实现了相关方法
 */
public class RSAEncryptionService extends AbstractEncryptionService {

    /**
     * RSA算法名称
     */
    private static final String ALGORITHM = "RSA";

    /**
     * RSA加密模式和填充方式
     */
    @Setter
    private String transformation;

    /**
     * RSA密钥长度
     */
    @Setter
    private int keySize;

    /**
     * 默认构造函数
     */
    public RSAEncryptionService() {
        this.transformation = "RSA/ECB/PKCS1Padding";
        this.keySize = 2048;
    }

    /**
     * 手动构造函数
     *
     * @param transformation 转换模式
     * @param keySize 密钥长度
     */
    public RSAEncryptionService(String transformation, int keySize) {
        this.transformation = transformation;
        this.keySize = keySize;
        validateParameters();
    }

    /**
     * 验证参数
     */
    private void validateParameters() {
        if (transformation == null || transformation.isEmpty()) {
            throw new IllegalArgumentException("RSA transformation cannot be null or empty");
        }

        // 验证密钥长度
        if (keySize < 1024) {
            throw new IllegalArgumentException("RSA key size must be at least 1024 bits");
        }
    }

    @Override
    public String getType() {
        return "RSA";
    }

    @Override
    public String encrypt(String data, String key, String iv) throws EncryptionException {
        try {
            Cipher cipher = Cipher.getInstance(transformation);
            PublicKey publicKey = getPublicKey(key);

            cipher.init(Cipher.ENCRYPT_MODE, publicKey);
            byte[] encrypted = cipher.doFinal(data.getBytes(getCharset()));

            return bytesToBase64(encrypted);
        } catch (Exception e) {
            throw new EncryptionException("RSA encryption failed", e);
        }
    }

    @Override
    public String decrypt(String encryptedData, String key, String iv) throws EncryptionException {
        try {
            Cipher cipher = Cipher.getInstance(transformation);
            PrivateKey privateKey = getPrivateKey(key);

            cipher.init(Cipher.DECRYPT_MODE, privateKey);
            byte[] decrypted = cipher.doFinal(base64ToBytes(encryptedData));

            return new String(decrypted, getCharset());
        } catch (Exception e) {
            throw new EncryptionException("RSA decryption failed", e);
        }
    }

    @Override
    public String generateIv() {
        // RSA不使用IV，但为了接口一致性，返回一个空字符串
        return "";
    }

    /**
     * 获取RSA公钥
     *
     * @param key Base64编码的公钥字符串
     * @return 公钥对象
     */
    private PublicKey getPublicKey(String key) throws Exception {
        byte[] keyBytes = base64ToBytes(key);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(ALGORITHM);
        return keyFactory.generatePublic(keySpec);
    }

    /**
     * 获取RSA私钥
     *
     * @param key Base64编码的私钥字符串
     * @return 私钥对象
     */
    private PrivateKey getPrivateKey(String key) throws Exception {
        byte[] keyBytes = base64ToBytes(key);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(ALGORITHM);
        return keyFactory.generatePrivate(keySpec);
    }

    /**
     * 生成RSA密钥对
     *
     * @return 包含公钥和私钥的数组，索引0为公钥，索引1为私钥
     */
    public String[] generateKeyPair() throws EncryptionException {
        try {
            KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance(ALGORITHM);
            keyPairGenerator.initialize(keySize);
            KeyPair keyPair = keyPairGenerator.generateKeyPair();

            String publicKey = bytesToBase64(keyPair.getPublic().getEncoded());
            String privateKey = bytesToBase64(keyPair.getPrivate().getEncoded());

            return new String[] { publicKey, privateKey };
        } catch (Exception e) {
            throw new EncryptionException("Failed to generate RSA key pair", e);
        }
    }

    @Override
    public SecretKeyBO generateSecretKey() {
        try {
            String[] keyPair = generateKeyPair();
            return new SecretKeyBO(keyPair[0], keyPair[1]);
        } catch (Exception e) {
            throw new EncryptionException("Failed to generate RSA secret key", e);
        }
    }
}
