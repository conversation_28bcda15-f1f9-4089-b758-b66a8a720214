package cn.iocoder.yudao.framework.crypto.core;

import org.springframework.stereotype.Component;

import java.util.Base64;

/**
 * Base64编码服务实现
 *
 * 注意：Base64不是加密算法，而是编码方式，不提供安全性
 */
@Component("base64Encoding")
public class Base64EncodingService extends AbstractEncryptionService {

    @Override
    public String getType() {
        return "Base64";
    }

    @Override
    public String encrypt(String data, String key) throws EncryptionException {
        return encrypt(data, key,null);
    }

    @Override
    public String decrypt(String encryptedData, String key) throws EncryptionException {
        return decrypt(encryptedData, key,null);
    }

    @Override
    public String encrypt(String data, String key, String iv) throws EncryptionException {
        try {
            // Base64编码不使用密钥和IV，忽略这些参数
            byte[] dataBytes = data.getBytes(getCharset());
            return Base64.getEncoder().encodeToString(dataBytes);
        } catch (Exception e) {
            throw new EncryptionException("Base64 encoding failed", e);
        }
    }

    @Override
    public String decrypt(String encryptedData, String key, String iv) throws EncryptionException {
        try {
            // Base64解码不使用密钥和IV，忽略这些参数
            byte[] decodedBytes = Base64.getDecoder().decode(encryptedData);
            return new String(decodedBytes, getCharset());
        } catch (Exception e) {
            throw new EncryptionException("Base64 decoding failed", e);
        }
    }

    @Override
    public String generateIv() {
        // Base64不使用IV，但为了接口一致性，返回一个空字符串
        return "";
    }
}
