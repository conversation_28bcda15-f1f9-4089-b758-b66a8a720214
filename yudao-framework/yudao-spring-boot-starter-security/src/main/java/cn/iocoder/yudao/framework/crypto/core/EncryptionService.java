package cn.iocoder.yudao.framework.crypto.core;


/**
 * 加密解密服务接口
 *
 * 提供统一的加密解密操作，支持不同的加密算法和密钥
 */
public interface EncryptionService {

    /**
     * 获取加密算法类型
     *
     * @return 加密算法类型
     */
    String getType();

    /**
     * 使用默认密钥加密数据
     *
     * @param data 待加密的原始数据
     * @return 加密后的数据
     * @throws EncryptionException 加密过程中发生错误
     */
    String encrypt(String data) throws EncryptionException;

    /**
     * 使用默认密钥解密数据
     *
     * @param encryptedData 加密后的数据
     * @return 解密后的原始数据
     * @throws EncryptionException 解密过程中发生错误
     */
    String decrypt(String encryptedData) throws EncryptionException;

}
