package cn.iocoder.yudao.framework.crypto.core;

import lombok.Setter;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.ByteBuffer;
import java.security.SecureRandom;
import java.util.Arrays;
import java.util.Base64;

/**
 * AES-GCM对称加密服务实现
 *
 * 使用AES-256-GCM模式，提供认证加密，是目前最安全的对称加密算法之一
 * 支持UTF-8编码，前端可以使用CryptoJS、Web Crypto API等库实现兼容
 */
public class AESGCMEncryptionService extends AbstractEncryptionService {

    /**
     * AES算法名称
     */
    private static final String ALGORITHM = "AES";

    /**
     * AES-GCM加密模式和填充方式
     */
    private static final String TRANSFORMATION = "AES/GCM/NoPadding";

    /**
     * GCM认证标签长度（比特）
     */
    private static final int GCM_TAG_LENGTH = 128;

    /**
     * GCM随机数长度（字节）
     */
    private static final int GCM_NONCE_LENGTH = 12;

    /**
     * AES密钥长度（比特）
     */
    @Setter
    private int keySize;

    /**
     * 默认构造函数
     */
    public AESGCMEncryptionService() {
        this.keySize = 256;
    }

    /**
     * 构造函数
     *
     * @param keySize 密钥长度
     */
    public AESGCMEncryptionService(int keySize) {
        this.keySize = keySize;
        validateParameters();
    }

    /**
     * 验证参数
     */
    private void validateParameters() {
        // 验证密钥长度是否是AES支持的值
        if (keySize != 128 && keySize != 192 && keySize != 256) {
            throw new IllegalArgumentException("AES key size must be 128, 192, or 256 bits");
        }
    }

    @Override
    public String getType() {
        return "AES-GCM";
    }

    @Override
    public String encrypt(String data, String key, String iv) throws EncryptionException {
        try {
            // 创建密钥
            SecretKey secretKey = createKey(key);

            // 使用提供的IV（如果为空则生成新的）
            byte[] nonce;
            if (iv == null || iv.isEmpty()) {
                nonce = generateNonce();
            } else {
                nonce = base64ToBytes(iv);
                if (nonce.length != GCM_NONCE_LENGTH) {
                    throw new EncryptionException("Invalid GCM nonce length. Expected: " + GCM_NONCE_LENGTH + " bytes");
                }
            }

            // 初始化加密器
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            GCMParameterSpec parameterSpec = new GCMParameterSpec(GCM_TAG_LENGTH, nonce);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, parameterSpec);

            // 加密数据
            byte[] dataBytes = data.getBytes(getCharset());
            byte[] encryptedBytes = cipher.doFinal(dataBytes);

            // 将nonce和密文组合在一起
            ByteBuffer byteBuffer = ByteBuffer.allocate(nonce.length + encryptedBytes.length);
            byteBuffer.put(nonce);
            byteBuffer.put(encryptedBytes);

            // 返回Base64编码的结果
            return Base64.getEncoder().encodeToString(byteBuffer.array());
        } catch (Exception e) {
            throw new EncryptionException("AES-GCM encryption failed", e);
        }
    }

    @Override
    public String decrypt(String encryptedData, String key, String iv) throws EncryptionException {
        try {
            // 解码Base64
            byte[] encryptedBytes = Base64.getDecoder().decode(encryptedData);

            // 提取nonce和密文
            ByteBuffer byteBuffer = ByteBuffer.wrap(encryptedBytes);
            byte[] nonce = new byte[GCM_NONCE_LENGTH];
            byteBuffer.get(nonce);

            byte[] ciphertext = new byte[byteBuffer.remaining()];
            byteBuffer.get(ciphertext);

            // 创建密钥
            SecretKey secretKey = createKey(key);

            // 初始化解密器
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            GCMParameterSpec parameterSpec = new GCMParameterSpec(GCM_TAG_LENGTH, nonce);
            cipher.init(Cipher.DECRYPT_MODE, secretKey, parameterSpec);

            // 解密数据
            byte[] decryptedBytes = cipher.doFinal(ciphertext);

            // 返回UTF-8编码的结果
            return new String(decryptedBytes, getCharset());
        } catch (Exception e) {
            throw new EncryptionException("AES-GCM decryption failed", e);
        }
    }

    @Override
    public String generateIv() {
        try {
            byte[] nonce = generateNonce();
            return Base64.getEncoder().encodeToString(nonce);
        } catch (Exception e) {
            throw new EncryptionException("Failed to generate GCM nonce", e);
        }
    }

    /**
     * 生成GCM随机数
     *
     * @return GCM随机数字节数组
     */
    private byte[] generateNonce() {
        byte[] nonce = new byte[GCM_NONCE_LENGTH];
        new SecureRandom().nextBytes(nonce);
        return nonce;
    }

    /**
     * 创建AES密钥
     *
     * @param key 密钥字符串
     * @return 密钥对象
     */
    private SecretKey createKey(String key) {
        // 确保密钥长度正确（256位 = 32字节）
        byte[] keyBytes = key.getBytes(getCharset());
        byte[] normalizedKey = new byte[keySize / 8];

        if (keyBytes.length < normalizedKey.length) {
            // 如果密钥太短，填充到所需长度
            System.arraycopy(keyBytes, 0, normalizedKey, 0, keyBytes.length);
            // 使用PKCS5Padding风格填充
            byte padValue = (byte)(normalizedKey.length - keyBytes.length);
            Arrays.fill(normalizedKey, keyBytes.length, normalizedKey.length, padValue);
        } else {
            // 如果密钥太长，截断到所需长度
            System.arraycopy(keyBytes, 0, normalizedKey, 0, normalizedKey.length);
        }

        return new SecretKeySpec(normalizedKey, ALGORITHM);
    }

    /**
     * 生成随机AES密钥
     *
     * @return Base64编码的随机密钥
     */
    public String generateRandomKey() {
        try {
            byte[] key = new byte[keySize / 8];
            new SecureRandom().nextBytes(key);
            return Base64.getEncoder().encodeToString(key);
        } catch (Exception e) {
            throw new EncryptionException("Failed to generate random AES key", e);
        }
    }
}
